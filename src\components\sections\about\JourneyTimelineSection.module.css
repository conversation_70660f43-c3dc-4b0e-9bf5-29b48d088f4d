/* Journey Timeline Section Styles - Optimized for Natural Scrolling */

.timelineSection {
  /* Ensure completely natural browser scrolling */
  overflow-x: hidden;
  /* Force natural scroll behavior and override any global settings */
  scroll-behavior: auto !important;
  /* Ensure no transform interference */
  transform: none;
  /* Remove any potential scroll snap */
  scroll-snap-type: none;
}

.timelineContainer {
  /* Optimize for transform animations but don't interfere with scroll */
  will-change: transform;
  /* Force natural scroll behavior */
  scroll-behavior: auto !important;
  /* Ensure no interference with parent scroll */
  position: relative;
  /* Remove any potential scroll constraints */
  overflow: visible;
}

.timelinePath {
  filter: url(#timeline-glow);
}

.avatarFallback {
  display: none;
}

/* Completely natural animations that follow browser scrolling */
.slowScrollContainer {
  /* Remove any custom timing - let browser handle it */
  --transition-duration: 0s; /* No artificial transitions */
  --ease-curve: linear; /* Linear to match scroll */
}

/* Natural card animations that don't interfere with scroll */
.timelineCard {
  /* Only animate non-scroll properties */
  transition: box-shadow 0.2s ease-out, border-color 0.2s ease-out;
  transform-origin: center center;
}

.timelineCard:hover {
  transition: box-shadow 0.15s ease-out, border-color 0.15s ease-out, transform 0.15s ease-out;
}
