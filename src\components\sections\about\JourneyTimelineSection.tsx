import { useState, useRef, useLayoutEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform, MotionValue } from 'framer-motion';
import { Lightbulb } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types';
import styles from './JourneyTimelineSection.module.css';

const timelineData = [
  {
    date: '2021',
    title: 'The Beginning',
    category: 'Foundation',
    description: 'Started my Computer Engineering degree during the pandemic. Despite having no laptop and slow internet, I passed all my subjects through determination.',
    keyLearning: 'Resilience and resourcefulness are crucial for overcoming obstacles.',
    align: 'left' as const,
  },
  {
    date: '2022',
    title: 'Discovering a Passion',
    category: 'Discovery',
    description: 'With the start of face-to-face classes, I discovered my passion for programming. I used university labs to learn HTML, CSS, & JavaScript and built my first website.',
    keyLearning: 'The moment I built something for the web, I knew this was what I wanted to do.',
    align: 'right' as const,
  },
  {
    date: '2023',
    title: 'A Turning Point',
    category: 'Growth',
    description: 'Getting my own laptop changed everything. I could finally code anytime, leading to many late nights spent learning and building small projects.',
    keyLearning: 'Having the right tools is a catalyst for rapid growth and continuous learning.',
    align: 'left' as const,
  },
  {
    date: '2024',
    title: 'Real-World Experience',
    category: 'Internship',
    description: "As an intern on a hospital's IT team, I worked on real websites and learned how technology helps people, bridging the gap between theory and practice.",
    keyLearning: 'Applying skills in a professional environment is the key to understanding real-world impact.',
    align: 'right' as const,
  },
  {
    date: '2025',
    title: 'Ready for Impact',
    category: 'Career',
    description: 'After graduating with a B.S. in Computer Engineering, I am now focused on building performant web applications and seeking a full-stack developer role.',
    keyLearning: 'Eager to apply my skills to solve business problems and begin my professional career.',
    align: 'left' as const,
  },
];

// Reusable animated card component
const TimelineCard = ({ 
  item, 
  index,
  activeTimelineItem,
  setActiveTimelineItem,
  scrollYProgress 
}: { 
  item: typeof timelineData[0], 
  index: number,
  activeTimelineItem: number | null,
  setActiveTimelineItem: (index: number | null) => void,
  scrollYProgress: MotionValue<number>
}) => {
  // Each card is active in a specific segment of the scroll progress
  const start = index / timelineData.length;
  const end = start + 1 / timelineData.length;
  
  // Very subtle scale animation that doesn't interfere with natural scrolling
  const scale = useTransform(
    scrollYProgress,
    [start - 0.05, start + 0.05, end - 0.05, end + 0.05],
    [1, 1.01, 1.01, 1], // Minimal scaling for natural feel
    { clamp: true }
  );
  
  return (
    <motion.div
      key={index}
      variants={itemVariants}
      style={{ scale }}
      className={cn(
        "relative w-full md:w-5/12",
        item.align === 'left' ? 'md:self-start' : 'md:self-end'
      )}
    >
      <Card
        className={cn(
          'cursor-pointer transform hover:scale-[1.02]',
          'bg-neutral-900/60 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl border-neutral-800/80',
          'hover:-translate-y-1 hover:shadow-xl hover:shadow-purple-500/10 hover:border-neutral-700/60',
          activeTimelineItem === index && 'ring-2 ring-primary/50 shadow-lg shadow-purple-500/20',
          styles.timelineCard
        )}
        onClick={() => setActiveTimelineItem(activeTimelineItem === index ? null : index)}
      >
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold">{item.title}</CardTitle>
              <CardDescription className="text-primary font-medium mt-1">
                {item.date}
              </CardDescription>
            </div>
            <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
              {item.category}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground leading-relaxed mb-4">
            {item.description}
          </p>
          <AnimatePresence>
            {activeTimelineItem === index && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="border-t border-border/50 pt-4 mt-4"
              >
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm font-medium text-primary">
                    <Lightbulb className="w-4 h-4" />
                    Key Takeaway
                  </div>
                  <p className="text-sm text-muted-foreground italic">
                    "{item.keyLearning}"
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default function JourneyTimelineSection({ className }: BaseSectionProps) {
  const [activeTimelineItem, setActiveTimelineItem] = useState<number | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const pathRef = useRef<SVGPathElement>(null);
  const [pathLength, setPathLength] = useState(0);

  useLayoutEffect(() => {
    if (pathRef.current) {
      setPathLength(pathRef.current.getTotalLength());
    }
  }, []);

  // Avatar animation starts when section top hits center viewport (50vh) and continues until section bottom leaves center
  const { scrollYProgress } = useScroll({
    target: timelineRef,
    offset: ['start 50%', 'end 50%'], // Start when section top hits 50vh, end when section bottom hits 50vh
  });

  // Direct 1:1 mapping of scroll progress to avatar movement
  const avatarPathProgress = useTransform(
    scrollYProgress,
    [0, 1],
    [0, 1]
  );

  const avatarPosition = useTransform(avatarPathProgress, (pos) => {
    if (pathRef.current && pathLength > 0) {
      // Clamp position to ensure it stays within bounds
      const clampedPos = Math.max(0, Math.min(1, pos));
      const point = pathRef.current.getPointAtLength(clampedPos * pathLength);
      return { x: point.x, y: point.y };
    }
    // Default position at the start of the path
    return { x: 100, y: 40 };
  });

  // Center the avatar (48px width/height) on the path point
  const avatarX = useTransform(avatarPosition, (pos) => pos.x - 24);
  const avatarY = useTransform(avatarPosition, (pos) => pos.y - 24);

  // Standard height for natural scrolling
  const totalHeight = timelineData.length * 300 + 200;

  return (
    <section
      className={cn('py-24 sm:py-32', styles.timelineSection, className)}
    >
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            A TIMELINE OF MY GROWTH
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
        >
          My{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            Journey
          </span>
        </motion.h2>
      </div>

      <div className="container mx-auto px-4 max-w-6xl">
        <div className="relative">
          {/* S-Curve SVG Background */}
          <div aria-hidden="true" className="absolute top-0 left-1/2 -translate-x-1/2 h-full w-full max-w-xs z-0">
            <svg 
              width="100%" 
              height={`${totalHeight}px`} 
              viewBox={`0 0 200 ${totalHeight}`} 
              preserveAspectRatio="xMidYMin meet" 
              className="overflow-visible"
            >
              <defs>
                <linearGradient id="journey-timeline-gradient" gradientTransform="rotate(90)">
                  <stop offset="0%" stopColor="#a855f7" />
                  <stop offset="25%" stopColor="#ec4899" />
                  <stop offset="50%" stopColor="#f97316" />
                  <stop offset="75%" stopColor="#3b82f6" />
                  <stop offset="100%" stopColor="#a855f7" />
                </linearGradient>
                <filter id="timeline-glow" x="-100%" y="-100%" width="300%" height="300%">
                  <feGaussianBlur stdDeviation="12" result="coloredBlur"/>
                  <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
                <mask id="timeline-mask">
                  <motion.path
                    d={`M 100,40 C 40,${totalHeight * 0.15} 160,${totalHeight * 0.32} 100,${totalHeight * 0.5} C 40,${totalHeight * 0.68} 160,${totalHeight * 0.85} 100,${totalHeight - 20}`}
                    fill="none"
                    stroke="white"
                    strokeWidth="12"
                    strokeLinecap="round"
                    style={{
                      strokeDasharray: `${pathLength} ${pathLength}`,
                      strokeDashoffset: useTransform(scrollYProgress, [0, 1], [pathLength, 0])
                    }}
                  />
                </mask>
              </defs>
              
              {/* Background S-curve track */}
              <path
                d={`M 100,40 C 40,${totalHeight * 0.15} 160,${totalHeight * 0.32} 100,${totalHeight * 0.5} C 40,${totalHeight * 0.68} 160,${totalHeight * 0.85} 100,${totalHeight - 20}`}
                fill="none"
                stroke="#373737"
                strokeWidth="8"
                strokeLinecap="round"
              />
              
              {/* Animated progress S-curve track */}
              <g mask="url(#timeline-mask)">
                <path
                  ref={pathRef}
                  d={`M 100,40 C 40,${totalHeight * 0.15} 160,${totalHeight * 0.32} 100,${totalHeight * 0.5} C 40,${totalHeight * 0.68} 160,${totalHeight * 0.85} 100,${totalHeight - 20}`}
                  fill="none"
                  stroke="url(#journey-timeline-gradient)"
                  strokeWidth="8"
                  strokeLinecap="round"
                  className={styles.timelinePath}
                />
              </g>
              
              {/* Avatar moving along the S-curve */}
              <motion.foreignObject
                x={avatarX}
                y={avatarY}
                width="48"
                height="48"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="w-full h-full">
                  <img
                    src="/image/profile.png"
                    alt="CJ Jutba's avatar"
                    className="w-full h-full rounded-full object-cover shadow-xl ring-2 ring-purple-500/30"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                  <div
                    className={cn("absolute inset-0 w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 shadow-xl ring-2 ring-purple-500/30 flex items-center justify-center text-white font-bold text-sm hidden", styles.avatarFallback)}
                  >
                    CJ
                  </div>
                </div>
              </motion.foreignObject>
            </svg>
          </div>

          {/* Timeline Content */}
          <motion.div
            ref={timelineRef}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainerVariants}
            className={cn("relative flex flex-col items-center gap-40 md:gap-44 list-none p-0 z-10", styles.timelineContainer)}
            style={{
              minHeight: `${totalHeight}px`,
              paddingBottom: '100px'
            }}
          >
            {timelineData.map((item, index) => (
              <TimelineCard
                key={index}
                item={item}
                index={index}
                activeTimelineItem={activeTimelineItem}
                setActiveTimelineItem={setActiveTimelineItem}
                scrollYProgress={scrollYProgress}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}