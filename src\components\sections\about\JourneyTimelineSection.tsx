import { useState, useRef, useLayoutEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform, MotionValue } from 'framer-motion';
import { Lightbulb } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { BaseSectionProps, staggerContainerVariants } from './types';
import styles from './JourneyTimelineSection.module.css';

const timelineData = [
  {
    date: '2021',
    title: 'The Beginning',
    category: 'Foundation',
    description: 'Started my Computer Engineering degree during the pandemic. Despite having no laptop and slow internet, I passed all my subjects through determination.',
    keyLearning: 'Resilience and resourcefulness are crucial for overcoming obstacles.',
    align: 'left' as const,
  },
  {
    date: '2022',
    title: 'Discovering a Passion',
    category: 'Discovery',
    description: 'With the start of face-to-face classes, I discovered my passion for programming. I used university labs to learn HTML, CSS, & JavaScript and built my first website.',
    keyLearning: 'The moment I built something for the web, I knew this was what I wanted to do.',
    align: 'right' as const,
  },
  {
    date: '2023',
    title: 'A Turning Point',
    category: 'Growth',
    description: 'Getting my own laptop changed everything. I could finally code anytime, leading to many late nights spent learning and building small projects.',
    keyLearning: 'Having the right tools is a catalyst for rapid growth and continuous learning.',
    align: 'left' as const,
  },
  {
    date: '2024',
    title: 'Real-World Experience',
    category: 'Internship',
    description: "As an intern on a hospital's IT team, I worked on real websites and learned how technology helps people, bridging the gap between theory and practice.",
    keyLearning: 'Applying skills in a professional environment is the key to understanding real-world impact.',
    align: 'right' as const,
  },
  {
    date: '2025',
    title: 'Ready for Impact',
    category: 'Career',
    description: 'After graduating with a B.S. in Computer Engineering, I am now focused on building performant web applications and seeking a full-stack developer role.',
    keyLearning: 'Eager to apply my skills to solve business problems and begin my professional career.',
    align: 'left' as const,
  },
];

// Enhanced Timeline Card with precise scroll-based animations
const TimelineCard = ({
  item,
  index,
  activeTimelineItem,
  setActiveTimelineItem,
  scrollYProgress
}: {
  item: typeof timelineData[0],
  index: number,
  activeTimelineItem: number | null,
  setActiveTimelineItem: (index: number | null) => void,
  scrollYProgress: MotionValue<number>
}) => {
  // Calculate precise scroll ranges for each card
  // Each card is active for 1/5th of the total scroll progress
  const cardSegment = 1 / timelineData.length;
  const cardStart = index * cardSegment;
  const cardEnd = (index + 1) * cardSegment;
  const cardCenter = cardStart + cardSegment / 2;

  // Smooth scale animation that peaks when card is perfectly centered
  const scale = useTransform(
    scrollYProgress,
    [
      Math.max(0, cardStart - 0.1),    // Start scaling before entering
      cardCenter,                       // Peak at center
      Math.min(1, cardEnd + 0.1)       // End scaling after leaving
    ],
    [0.95, 1.05, 0.95],               // Subtle but noticeable scale
    { clamp: true }
  );

  // Opacity for smooth fade transitions
  const opacity = useTransform(
    scrollYProgress,
    [
      Math.max(0, cardStart - 0.08),
      cardStart + 0.02,
      cardEnd - 0.02,
      Math.min(1, cardEnd + 0.08)
    ],
    [0.4, 1, 1, 0.4],
    { clamp: true }
  );

  // Y-axis translation for subtle floating effect
  const y = useTransform(
    scrollYProgress,
    [cardStart, cardCenter, cardEnd],
    [20, 0, 20],
    { clamp: true }
  );

  return (
    <motion.div
      style={{ scale, opacity, y }}
      className={cn(
        "relative w-full max-w-sm mx-auto sm:max-w-md md:max-w-lg",
        "px-4 sm:px-6",
        // Responsive alignment
        item.align === 'left'
          ? 'md:mr-auto md:ml-8 lg:ml-16 xl:ml-24'
          : 'md:ml-auto md:mr-8 lg:mr-16 xl:mr-24'
      )}
    >
      <Card
        className={cn(
          'cursor-pointer transition-all duration-300 ease-out',
          'bg-neutral-900/70 backdrop-blur-xl border-neutral-800/60',
          'shadow-[0_8px_32px_rgba(0,0,0,0.3)] rounded-2xl',
          'hover:shadow-[0_12px_40px_rgba(168,85,247,0.15)] hover:border-purple-500/30',
          'hover:-translate-y-1',
          activeTimelineItem === index && 'ring-2 ring-purple-500/40 shadow-[0_12px_40px_rgba(168,85,247,0.25)]',
          styles.timelineCard
        )}
        onClick={() => setActiveTimelineItem(activeTimelineItem === index ? null : index)}
      >
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-white">{item.title}</CardTitle>
              <CardDescription className="text-purple-400 font-medium mt-1">
                {item.date}
              </CardDescription>
            </div>
            <Badge
              variant="secondary"
              className="bg-purple-500/20 text-purple-300 border-purple-500/30 hover:bg-purple-500/30"
            >
              {item.category}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-neutral-300 leading-relaxed mb-4">
            {item.description}
          </p>
          <AnimatePresence>
            {activeTimelineItem === index && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="border-t border-neutral-700/50 pt-4 mt-4"
              >
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm font-medium text-purple-400">
                    <Lightbulb className="w-4 h-4" />
                    Key Takeaway
                  </div>
                  <p className="text-sm text-neutral-400 italic leading-relaxed">
                    "{item.keyLearning}"
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default function JourneyTimelineSection({ className }: BaseSectionProps) {
  const [activeTimelineItem, setActiveTimelineItem] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const pathRef = useRef<SVGPathElement>(null);
  const [pathLength, setPathLength] = useState(0);

  // Initialize path length for precise avatar positioning
  useLayoutEffect(() => {
    if (pathRef.current) {
      setPathLength(pathRef.current.getTotalLength());
    }
  }, []);

  // CRITICAL: Precise scroll trigger points for perfect centering
  // Avatar starts moving when first card enters viewport center
  // Avatar stops moving when last card leaves viewport center
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start center", "end center"] // Perfect viewport center alignment
  });

  // Calculate avatar position along the curved path
  // This ensures smooth, linear movement tied directly to scroll
  const avatarPathProgress = useTransform(
    scrollYProgress,
    [0, 1],
    [0, 1],
    { clamp: true } // Prevent overshooting
  );

  // Get precise point on SVG path for avatar positioning
  const avatarPosition = useTransform(avatarPathProgress, (progress) => {
    if (pathRef.current && pathLength > 0) {
      const clampedProgress = Math.max(0, Math.min(1, progress));
      const point = pathRef.current.getPointAtLength(clampedProgress * pathLength);
      return { x: point.x, y: point.y };
    }
    return { x: 100, y: 50 }; // Fallback position
  });

  // Center the 48px avatar on the path point
  const avatarX = useTransform(avatarPosition, (pos) => pos.x - 24);
  const avatarY = useTransform(avatarPosition, (pos) => pos.y - 24);

  // Calculate responsive timeline height
  // Each card gets enough space for smooth scrolling
  const timelineHeight = timelineData.length * 120 + 200; // Responsive height

  return (
    <section
      ref={containerRef}
      className={cn(
        'relative py-20 px-4 sm:px-6 lg:px-8',
        'bg-gradient-to-b from-background via-background/95 to-background',
        className
      )}
      style={{
        // Ensure sufficient height for smooth scrolling
        // Each card needs space to enter/exit viewport smoothly
        minHeight: `${timelineData.length * 100 + 50}vh`
      }}
    >
      {/* Header */}
      <motion.div
        variants={staggerContainerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        className="text-center mb-20 relative z-20"
      >
        <motion.p
          className="text-sm font-medium text-primary mb-4 tracking-wider uppercase"
        >
          A Timeline of My Growth
        </motion.p>
        <motion.h2
          className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6"
        >
          My <span className="text-primary italic font-serif">Journey</span>
        </motion.h2>
      </motion.div>

      {/* Timeline Container */}
      <div className="relative max-w-7xl mx-auto">
        {/* Timeline Path with Avatar */}
        <div
          ref={timelineRef}
          className="absolute left-1/2 transform -translate-x-1/2 w-1"
          style={{
            height: `${timelineHeight}vh`,
            top: '10vh' // Offset to center first card
          }}
        >
          {/* Curved Timeline Path - Responsive SVG */}
          <svg
            className="absolute inset-0 w-full h-full"
            viewBox={`0 0 200 ${timelineHeight}`}
            preserveAspectRatio="none"
            style={{
              width: 'clamp(120px, 15vw, 180px)',
              left: 'clamp(-60px, -7.5vw, -90px)'
            }}
          >
            <defs>
              <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="rgb(168, 85, 247)" />
                <stop offset="25%" stopColor="rgb(236, 72, 153)" />
                <stop offset="50%" stopColor="rgb(59, 130, 246)" />
                <stop offset="75%" stopColor="rgb(16, 185, 129)" />
                <stop offset="100%" stopColor="rgb(245, 158, 11)" />
              </linearGradient>
              <filter id="pathGlow">
                <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* Main curved path */}
            <path
              ref={pathRef}
              d={`M 100,50 Q 60,${timelineHeight * 0.2} 100,${timelineHeight * 0.35} Q 140,${timelineHeight * 0.5} 100,${timelineHeight * 0.65} Q 60,${timelineHeight * 0.8} 100,${timelineHeight - 50}`}
              stroke="url(#timelineGradient)"
              strokeWidth="4"
              fill="none"
              filter="url(#pathGlow)"
              className="drop-shadow-lg"
            />
          </svg>

          {/* Animated Avatar - Precisely positioned */}
          <motion.div
            style={{
              x: avatarX,
              y: avatarY
            }}
            className="absolute"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              delay: 0.8,
              type: "spring",
              stiffness: 200,
              damping: 25
            }}
          >
            <div className="relative">
              {/* Animated glow ring */}
              <div className="absolute inset-0 w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 animate-spin opacity-60 blur-sm"
                   style={{ animationDuration: '3s' }}></div>

              {/* Main avatar container */}
              <div className="relative w-14 h-14 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 p-0.5 shadow-2xl shadow-purple-500/50">
                <div className="w-full h-full rounded-full bg-background flex items-center justify-center border border-neutral-800">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg shadow-inner">
                    C
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
                strokeWidth="8"
                strokeLinecap="round"
              />
              
              {/* Animated progress S-curve track */}
              <g mask="url(#timeline-mask)">
                <path
                  ref={pathRef}
                  d={`M 100,40 C 40,${totalHeight * 0.15} 160,${totalHeight * 0.32} 100,${totalHeight * 0.5} C 40,${totalHeight * 0.68} 160,${totalHeight * 0.85} 100,${totalHeight - 20}`}
                  fill="none"
                  stroke="url(#journey-timeline-gradient)"
                  strokeWidth="8"
                  strokeLinecap="round"
                  className={styles.timelinePath}
                />
              </g>
              
              {/* Avatar moving along the S-curve */}
              <motion.foreignObject
                x={avatarX}
                y={avatarY}
                width="48"
                height="48"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="w-full h-full">
                  <img
                    src="/image/profile.png"
                    alt="CJ Jutba's avatar"
                    className="w-full h-full rounded-full object-cover shadow-xl ring-2 ring-purple-500/30"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                  <div
                    className={cn("absolute inset-0 w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 shadow-xl ring-2 ring-purple-500/30 flex items-center justify-center text-white font-bold text-sm hidden", styles.avatarFallback)}
                  >
                    CJ
                  </div>
                </div>
              </motion.foreignObject>
            </svg>
          </div>

          {/* Timeline Content */}
          <motion.div
            ref={timelineRef}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainerVariants}
            className={cn("relative flex flex-col items-center gap-40 md:gap-44 list-none p-0 z-10", styles.timelineContainer)}
            style={{
              minHeight: `${totalHeight}px`,
              paddingBottom: '100px'
            }}
          >
            {timelineData.map((item, index) => (
              <TimelineCard
                key={index}
                item={item}
                index={index}
                activeTimelineItem={activeTimelineItem}
                setActiveTimelineItem={setActiveTimelineItem}
                scrollYProgress={scrollYProgress}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}